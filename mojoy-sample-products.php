<?php
/**
 * MOJOY Sample Products Creation
 * Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create Sample Products for Mojoy Store
 */
function mojoy_create_sample_products() {
    if (!class_exists('WooCommerce')) {
        return;
    }
    
    // Check if products already exist
    if (get_option('mojoy_sample_products_created')) {
        return;
    }
    
    // Create product categories
    mojoy_create_product_categories();
    
    // Create sample products
    $products = array(
        array(
            'name' => 'Essential Comfort Tee',
            'description' => 'Our signature ultra-soft t-shirt crafted from premium organic cotton. Designed for all-day comfort with a timeless fit that moves with you. Every purchase funds 2 meals through our Kitchen Initiative.',
            'short_description' => 'Ultra-soft organic cotton tee with timeless comfort and purposeful impact.',
            'price' => 899,
            'sale_price' => 699,
            'sku' => 'MOJOY-TEE-001',
            'category' => 'T-Shirts',
            'featured' => true,
            'attributes' => array(
                'Size' => array('S', 'M', 'L', 'XL', 'XXL'),
                'Color' => array('White', 'Black', 'Navy', 'Heather Grey')
            )
        ),
        array(
            'name' => 'Breathe Easy Polo',
            'description' => 'Elevate your style with our premium polo shirt featuring moisture-wicking technology and breathable fabric. Perfect for work, casual outings, or weekend adventures. Funds 2 meals per purchase.',
            'short_description' => 'Premium polo with moisture-wicking technology and breathable comfort.',
            'price' => 1299,
            'sku' => 'MOJOY-POLO-001',
            'category' => 'Polos',
            'featured' => true,
            'attributes' => array(
                'Size' => array('S', 'M', 'L', 'XL', 'XXL'),
                'Color' => array('White', 'Navy', 'Forest Green', 'Burgundy')
            )
        ),
        array(
            'name' => 'Movement Henley',
            'description' => 'A versatile henley that transitions seamlessly from day to night. Made with our signature soft-touch fabric that feels like a second skin. Designed for comfort, styled for life.',
            'short_description' => 'Versatile henley with signature soft-touch fabric for all-day comfort.',
            'price' => 1099,
            'sku' => 'MOJOY-HENLEY-001',
            'category' => 'Essentials',
            'featured' => false,
            'attributes' => array(
                'Size' => array('S', 'M', 'L', 'XL', 'XXL'),
                'Color' => array('Charcoal', 'Olive', 'Cream', 'Navy')
            )
        ),
        array(
            'name' => 'Comfort Crew Sweatshirt',
            'description' => 'Cozy up in our premium crew sweatshirt made from sustainable materials. Perfect for layering or wearing solo. Soft, warm, and consciously crafted for maximum comfort.',
            'short_description' => 'Premium crew sweatshirt made from sustainable materials.',
            'price' => 1599,
            'sale_price' => 1299,
            'sku' => 'MOJOY-CREW-001',
            'category' => 'Essentials',
            'featured' => true,
            'attributes' => array(
                'Size' => array('S', 'M', 'L', 'XL', 'XXL'),
                'Color' => array('Heather Grey', 'Navy', 'Black', 'Sage Green')
            )
        ),
        array(
            'name' => 'Everyday Comfort Shorts',
            'description' => 'Lightweight, breathable shorts perfect for warm weather and active days. Made with quick-dry fabric and featuring a comfortable elastic waistband. Your go-to for comfort and style.',
            'short_description' => 'Lightweight, breathable shorts with quick-dry fabric.',
            'price' => 799,
            'sku' => 'MOJOY-SHORTS-001',
            'category' => 'Essentials',
            'featured' => false,
            'attributes' => array(
                'Size' => array('S', 'M', 'L', 'XL', 'XXL'),
                'Color' => array('Khaki', 'Navy', 'Black', 'Olive')
            )
        ),
        array(
            'name' => 'Premium V-Neck Tee',
            'description' => 'A classic v-neck tee with a modern twist. Crafted from our premium cotton blend for superior softness and durability. The perfect foundation piece for any wardrobe.',
            'short_description' => 'Classic v-neck tee with premium cotton blend for superior softness.',
            'price' => 949,
            'sku' => 'MOJOY-VNECK-001',
            'category' => 'T-Shirts',
            'featured' => false,
            'attributes' => array(
                'Size' => array('S', 'M', 'L', 'XL', 'XXL'),
                'Color' => array('White', 'Black', 'Grey', 'Navy')
            )
        )
    );
    
    foreach ($products as $product_data) {
        mojoy_create_product($product_data);
    }
    
    // Mark as created
    update_option('mojoy_sample_products_created', true);
}

/**
 * Create Product Categories
 */
function mojoy_create_product_categories() {
    $categories = array(
        array(
            'name' => 'T-Shirts',
            'slug' => 't-shirts',
            'description' => 'Premium comfort t-shirts crafted from the finest materials'
        ),
        array(
            'name' => 'Polos',
            'slug' => 'polos',
            'description' => 'Elevated polo shirts for work and weekend'
        ),
        array(
            'name' => 'Essentials',
            'slug' => 'essentials',
            'description' => 'Wardrobe essentials for everyday comfort'
        ),
        array(
            'name' => 'New Arrivals',
            'slug' => 'new-arrivals',
            'description' => 'Latest additions to our comfort collection'
        ),
        array(
            'name' => 'Bestsellers',
            'slug' => 'bestsellers',
            'description' => 'Customer favorites and top-rated items'
        )
    );
    
    foreach ($categories as $category) {
        if (!term_exists($category['slug'], 'product_cat')) {
            wp_insert_term(
                $category['name'],
                'product_cat',
                array(
                    'slug' => $category['slug'],
                    'description' => $category['description']
                )
            );
        }
    }
}

/**
 * Create Individual Product
 */
function mojoy_create_product($product_data) {
    // Create the product
    $product = new WC_Product_Variable();
    
    $product->set_name($product_data['name']);
    $product->set_description($product_data['description']);
    $product->set_short_description($product_data['short_description']);
    $product->set_sku($product_data['sku']);
    $product->set_status('publish');
    $product->set_catalog_visibility('visible');
    $product->set_featured($product_data['featured']);
    
    // Set category
    $category_term = get_term_by('name', $product_data['category'], 'product_cat');
    if ($category_term) {
        $product->set_category_ids(array($category_term->term_id));
    }
    
    // Save the product
    $product_id = $product->save();
    
    // Create attributes and variations
    if (isset($product_data['attributes'])) {
        mojoy_create_product_variations($product_id, $product_data);
    } else {
        // Simple product
        $product = new WC_Product_Simple($product_id);
        $product->set_regular_price($product_data['price']);
        if (isset($product_data['sale_price'])) {
            $product->set_sale_price($product_data['sale_price']);
        }
        $product->save();
    }
    
    // Add custom meta for impact messaging
    update_post_meta($product_id, '_mojoy_meals_funded', 2);
    update_post_meta($product_id, '_mojoy_comfort_rating', rand(45, 50) / 10);
    update_post_meta($product_id, '_mojoy_sustainability_score', rand(85, 95));
    
    return $product_id;
}

/**
 * Create Product Variations
 */
function mojoy_create_product_variations($product_id, $product_data) {
    $product = wc_get_product($product_id);
    
    // Create attributes
    $attributes = array();
    $attribute_position = 0;
    
    foreach ($product_data['attributes'] as $attribute_name => $attribute_values) {
        $attribute = new WC_Product_Attribute();
        $attribute->set_id(0);
        $attribute->set_name($attribute_name);
        $attribute->set_options($attribute_values);
        $attribute->set_position($attribute_position);
        $attribute->set_visible(true);
        $attribute->set_variation(true);
        
        $attributes[] = $attribute;
        $attribute_position++;
    }
    
    $product->set_attributes($attributes);
    $product->save();
    
    // Create variations
    $sizes = $product_data['attributes']['Size'];
    $colors = $product_data['attributes']['Color'];
    
    foreach ($sizes as $size) {
        foreach ($colors as $color) {
            $variation = new WC_Product_Variation();
            $variation->set_parent_id($product_id);
            
            // Set variation attributes
            $variation->set_attributes(array(
                'Size' => $size,
                'Color' => $color
            ));
            
            // Set pricing with slight variations
            $base_price = $product_data['price'];
            $price_variation = rand(-50, 50);
            $regular_price = $base_price + $price_variation;
            
            $variation->set_regular_price($regular_price);
            
            if (isset($product_data['sale_price'])) {
                $sale_price = $product_data['sale_price'] + $price_variation;
                $variation->set_sale_price($sale_price);
            }
            
            // Set stock
            $variation->set_stock_quantity(rand(10, 50));
            $variation->set_manage_stock(true);
            $variation->set_stock_status('instock');
            
            // Set SKU
            $variation->set_sku($product_data['sku'] . '-' . strtoupper(substr($size, 0, 1)) . '-' . strtoupper(substr($color, 0, 3)));
            
            $variation->save();
        }
    }
}

/**
 * Add Sample Product Reviews
 */
function mojoy_add_sample_reviews() {
    if (get_option('mojoy_sample_reviews_created')) {
        return;
    }
    
    $products = wc_get_products(array('limit' => -1));
    
    $sample_reviews = array(
        array(
            'rating' => 5,
            'comment' => 'Absolutely love this! The fabric is incredibly soft and the fit is perfect. Knowing my purchase helps fund meals makes it even better.',
            'author' => 'Priya S.'
        ),
        array(
            'rating' => 5,
            'comment' => 'Best t-shirt I\'ve ever owned. The quality is outstanding and it feels great to support a brand with such a meaningful mission.',
            'author' => 'Rahul M.'
        ),
        array(
            'rating' => 4,
            'comment' => 'Great quality and super comfortable. The impact messaging really resonates with me. Will definitely order more!',
            'author' => 'Anita K.'
        ),
        array(
            'rating' => 5,
            'comment' => 'Finally found a brand that combines style, comfort, and purpose. The fabric breathes well and the fit is true to size.',
            'author' => 'Vikram T.'
        )
    );
    
    foreach ($products as $product) {
        // Add 2-3 random reviews per product
        $num_reviews = rand(2, 3);
        for ($i = 0; $i < $num_reviews; $i++) {
            $review = $sample_reviews[array_rand($sample_reviews)];
            
            $comment_data = array(
                'comment_post_ID' => $product->get_id(),
                'comment_author' => $review['author'],
                'comment_author_email' => strtolower(str_replace(' ', '', $review['author'])) . '@example.com',
                'comment_content' => $review['comment'],
                'comment_type' => 'review',
                'comment_approved' => 1,
                'comment_date' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
            );
            
            $comment_id = wp_insert_comment($comment_data);
            
            if ($comment_id) {
                add_comment_meta($comment_id, 'rating', $review['rating']);
            }
        }
    }
    
    update_option('mojoy_sample_reviews_created', true);
}

/**
 * Initialize Sample Data Creation
 */
function mojoy_init_sample_data() {
    // Only create sample data if explicitly requested
    if (isset($_GET['create_mojoy_samples']) && $_GET['create_mojoy_samples'] === 'true') {
        mojoy_create_sample_products();
        mojoy_add_sample_reviews();
        
        wp_redirect(admin_url('edit.php?post_type=product&mojoy_samples=created'));
        exit;
    }
}
add_action('admin_init', 'mojoy_init_sample_data');

/**
 * Add Admin Notice for Sample Data Creation
 */
function mojoy_sample_data_admin_notice() {
    if (!get_option('mojoy_sample_products_created') && current_user_can('manage_options')) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>Mojoy Setup:</strong> Would you like to create sample products to showcase your store? ';
        echo '<a href="' . admin_url('?create_mojoy_samples=true') . '" class="button button-primary">Create Sample Products</a></p>';
        echo '</div>';
    }
    
    if (isset($_GET['mojoy_samples']) && $_GET['mojoy_samples'] === 'created') {
        echo '<div class="notice notice-success is-dismissible">';
        echo '<p><strong>Success!</strong> Mojoy sample products and reviews have been created successfully.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'mojoy_sample_data_admin_notice');
