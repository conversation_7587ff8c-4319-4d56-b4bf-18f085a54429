<?php
/**
 * MOJOY Custom Functions
 * Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue Mojoy Custom Styles
 */
if (!function_exists('mojoy_enqueue_custom_styles')) {
function mojoy_enqueue_custom_styles() {
    wp_enqueue_style(
        'mojoy-custom-styles',
        get_template_directory_uri() . '/mojoy-custom-styles.css',
        array(),
        '1.0.0'
    );
    
    // Add Google Fonts
    wp_enqueue_style(
        'mojoy-google-fonts',
        'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap',
        array(),
        null
    );
}
}
add_action('wp_enqueue_scripts', 'mojoy_enqueue_custom_styles');

/**
 * Add Mojoy Brand Classes to Body
 */
function mojoy_body_classes($classes) {
    $classes[] = 'mojoy-brand';
    $classes[] = 'premium-comfort';
    return $classes;
}
add_filter('body_class', 'mojoy_body_classes');

/**
 * Customize WooCommerce
 */
function mojoy_woocommerce_setup() {
    // Add theme support for WooCommerce
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
}
add_action('after_setup_theme', 'mojoy_woocommerce_setup');

/**
 * Add Impact Message to Product Pages
 */
function mojoy_add_impact_message() {
    echo '<div class="mojoy-impact-message">';
    echo '<p><strong>🍽️ Your purchase helps fund meals through the Mojoy Kitchen Initiative</strong></p>';
    echo '</div>';
}
add_action('woocommerce_single_product_summary', 'mojoy_add_impact_message', 25);

/**
 * Customize Add to Cart Button Text
 */
function mojoy_custom_add_to_cart_text($text) {
    if (is_product()) {
        return 'Add to Cart - Make Impact';
    }
    return $text;
}
add_filter('woocommerce_product_add_to_cart_text', 'mojoy_custom_add_to_cart_text');

/**
 * Add Custom Product Badges
 */
function mojoy_product_badges() {
    global $product;
    
    if ($product->is_featured()) {
        echo '<span class="mojoy-product-badge featured">Premium Choice</span>';
    }
    
    if ($product->is_on_sale()) {
        echo '<span class="mojoy-product-badge sale">Limited Time</span>';
    }
}
add_action('woocommerce_before_shop_loop_item_title', 'mojoy_product_badges', 5);

/**
 * Customize Cart Page Messages
 */
function mojoy_cart_impact_message() {
    if (is_cart() && !WC()->cart->is_empty()) {
        $cart_total = WC()->cart->get_cart_contents_count();
        $meals_funded = $cart_total * 2; // 2 meals per item
        
        echo '<div class="mojoy-cart-impact">';
        echo '<h3>🌟 Your Impact</h3>';
        echo '<p>This order will help fund <strong>' . $meals_funded . ' meals</strong> for underserved communities!</p>';
        echo '</div>';
    }
}
add_action('woocommerce_before_cart_table', 'mojoy_cart_impact_message');

/**
 * Add Newsletter Signup Shortcode
 */
function mojoy_newsletter_signup($atts) {
    $atts = shortcode_atts(array(
        'title' => 'Join the Movement',
        'subtitle' => 'Get updates on new products and our impact',
    ), $atts);
    
    ob_start();
    ?>
    <div class="mojoy-newsletter-signup">
        <h3><?php echo esc_html($atts['title']); ?></h3>
        <p><?php echo esc_html($atts['subtitle']); ?></p>
        <form class="mojoy-newsletter-form">
            <input type="email" placeholder="Enter your email" required>
            <button type="submit" class="mojoy-btn-primary">Subscribe</button>
        </form>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('mojoy_newsletter', 'mojoy_newsletter_signup');

/**
 * Add Impact Counter Shortcode
 */
function mojoy_impact_counter($atts) {
    $atts = shortcode_atts(array(
        'meals' => '10000',
        'customers' => '5000',
        'communities' => '25',
    ), $atts);
    
    ob_start();
    ?>
    <div class="mojoy-impact-counter">
        <div class="impact-stat">
            <span class="number"><?php echo esc_html($atts['meals']); ?>+</span>
            <span class="label">Meals Funded</span>
        </div>
        <div class="impact-stat">
            <span class="number"><?php echo esc_html($atts['customers']); ?>+</span>
            <span class="label">Happy Customers</span>
        </div>
        <div class="impact-stat">
            <span class="number"><?php echo esc_html($atts['communities']); ?>+</span>
            <span class="label">Communities Served</span>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('mojoy_impact', 'mojoy_impact_counter');

/**
 * Customize Checkout Fields
 */
function mojoy_checkout_fields($fields) {
    // Add impact message to checkout
    $fields['billing']['billing_impact_message'] = array(
        'type' => 'textarea',
        'label' => 'Share why you\'re joining the Mojoy movement (optional)',
        'placeholder' => 'Tell us what inspired you to choose Mojoy...',
        'required' => false,
        'class' => array('form-row-wide'),
        'priority' => 999,
    );
    
    return $fields;
}
add_filter('woocommerce_checkout_fields', 'mojoy_checkout_fields');

/**
 * Add Custom CSS Variables to Head
 */
function mojoy_css_variables() {
    ?>
    <style>
    :root {
        --site-background: #ffffff;
        --content-width: 1200px;
        --border-radius: 8px;
        --transition: all 0.3s ease;
    }
    
    body.mojoy-brand {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background-color: var(--mojoy-light);
        color: var(--mojoy-text);
        line-height: 1.6;
    }
    
    .mojoy-container {
        max-width: var(--content-width);
        margin: 0 auto;
        padding: 0 20px;
    }
    </style>
    <?php
}
add_action('wp_head', 'mojoy_css_variables');

/**
 * Remove Default WooCommerce Styles (we'll use custom ones)
 */
add_filter('woocommerce_enqueue_styles', '__return_empty_array');

/**
 * Add Structured Data for SEO
 */
function mojoy_structured_data() {
    if (is_front_page()) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Mojoy',
            'description' => 'Premium comfort apparel with purposeful impact. Feel Good, Do Good.',
            'url' => home_url(),
            'logo' => get_template_directory_uri() . '/assets/images/mojoy-logo.png',
            'sameAs' => array(
                'https://instagram.com/mojoy',
                'https://facebook.com/mojoy',
                'https://twitter.com/mojoy'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }
}
add_action('wp_head', 'mojoy_structured_data');



/**
 * Custom Login Logo
 */
function mojoy_login_logo() {
    ?>
    <style type="text/css">
        #login h1 a {
            background-image: url('<?php echo get_template_directory_uri(); ?>/assets/images/mojoy-logo.png');
            background-size: contain;
            width: 200px;
            height: 80px;
        }
    </style>
    <?php
}
add_action('login_enqueue_scripts', 'mojoy_login_logo');


