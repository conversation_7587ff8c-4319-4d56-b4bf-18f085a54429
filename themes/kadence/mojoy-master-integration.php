<?php
/**
 * MOJOY MASTER INTEGRATION FILE
 * Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
 * 
 * This file integrates all Mojoy customizations and should be included in functions.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Include all Mojoy customization files
 */
function mojoy_include_customizations() {
    $customization_files = array(
        'mojoy-functions.php',
        'mojoy-shop-customizations.php',
        'mojoy-checkout-optimizations.php',
        'mojoy-navigation-footer.php',
        'mojoy-seo-performance.php',
        'mojoy-sample-products.php'
    );

    foreach ($customization_files as $file) {
        $file_path = get_template_directory() . '/' . $file;
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
}

// Only include if not already loaded
if (!function_exists('mojoy_enqueue_custom_styles')) {
    add_action('after_setup_theme', 'mojoy_include_customizations');
}

/**
 * Mojoy Theme Setup
 */
function mojoy_theme_setup() {
    // Add theme support
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('title-tag');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    
    // WooCommerce support
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'mojoy_theme_setup');

/**
 * Enqueue Mojoy Styles and Scripts
 */
function mojoy_enqueue_assets() {
    // Main stylesheet
    wp_enqueue_style('mojoy-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Custom styles
    wp_enqueue_style('mojoy-custom', get_template_directory_uri() . '/mojoy-custom-styles.css', array(), '1.0.0');
    
    // Google Fonts
    wp_enqueue_style('mojoy-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap', array(), null);
    
    // Custom JavaScript
    wp_enqueue_script('mojoy-scripts', get_template_directory_uri() . '/assets/js/mojoy-scripts.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('mojoy-scripts', 'mojoy_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('mojoy_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'mojoy_enqueue_assets');

/**
 * Mojoy Admin Dashboard Customizations
 */
function mojoy_admin_customizations() {
    // Add custom admin styles
    add_action('admin_head', 'mojoy_admin_styles');
    
    // Add Mojoy admin menu
    add_action('admin_menu', 'mojoy_admin_menu');
    
    // Add dashboard widgets
    add_action('wp_dashboard_setup', 'mojoy_dashboard_widgets');
}
add_action('admin_init', 'mojoy_admin_customizations');

/**
 * Custom Admin Styles
 */
function mojoy_admin_styles() {
    ?>
    <style>
    .mojoy-admin-header {
        background: linear-gradient(135deg, #1a252f, #2c3e50);
        color: white;
        padding: 20px;
        margin: 20px 0;
        border-radius: 8px;
    }
    
    .mojoy-admin-header h2 {
        color: white;
        margin: 0 0 10px 0;
    }
    
    .mojoy-impact-stats {
        display: flex;
        gap: 2rem;
        margin-top: 15px;
    }
    
    .mojoy-impact-stat {
        text-align: center;
    }
    
    .mojoy-impact-stat .number {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: #f39c12;
    }
    
    .mojoy-impact-stat .label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    </style>
    <?php
}

/**
 * Mojoy Admin Menu
 */
function mojoy_admin_menu() {
    add_menu_page(
        'Mojoy Settings',
        'Mojoy',
        'manage_options',
        'mojoy-settings',
        'mojoy_admin_page',
        'dashicons-heart',
        30
    );
    
    add_submenu_page(
        'mojoy-settings',
        'Impact Dashboard',
        'Impact Dashboard',
        'manage_options',
        'mojoy-impact',
        'mojoy_impact_dashboard'
    );
}

/**
 * Mojoy Admin Page
 */
function mojoy_admin_page() {
    ?>
    <div class="wrap">
        <div class="mojoy-admin-header">
            <h2>Mojoy - Feel Good, Do Good</h2>
            <p>Premium Comfort • Timeless Style • Purposeful Impact</p>
            <div class="mojoy-impact-stats">
                <div class="mojoy-impact-stat">
                    <span class="number">15,000+</span>
                    <span class="label">Meals Funded</span>
                </div>
                <div class="mojoy-impact-stat">
                    <span class="number">8,500+</span>
                    <span class="label">Happy Customers</span>
                </div>
                <div class="mojoy-impact-stat">
                    <span class="number">35+</span>
                    <span class="label">Communities Served</span>
                </div>
            </div>
        </div>
        
        <div class="mojoy-admin-content">
            <h3>Website Setup Status</h3>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Sample Products</td>
                        <td><?php echo get_option('mojoy_sample_products_created') ? '✅ Created' : '❌ Not Created'; ?></td>
                        <td>
                            <?php if (!get_option('mojoy_sample_products_created')): ?>
                                <a href="<?php echo admin_url('?create_mojoy_samples=true'); ?>" class="button button-primary">Create Sample Products</a>
                            <?php else: ?>
                                <a href="<?php echo admin_url('edit.php?post_type=product'); ?>" class="button">View Products</a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Homepage Content</td>
                        <td>✅ Ready</td>
                        <td><a href="<?php echo admin_url('post.php?post=' . get_option('page_on_front') . '&action=edit'); ?>" class="button">Edit Homepage</a></td>
                    </tr>
                    <tr>
                        <td>WooCommerce Setup</td>
                        <td><?php echo class_exists('WooCommerce') ? '✅ Active' : '❌ Not Active'; ?></td>
                        <td>
                            <?php if (!class_exists('WooCommerce')): ?>
                                <a href="<?php echo admin_url('plugin-install.php?s=woocommerce&tab=search&type=term'); ?>" class="button">Install WooCommerce</a>
                            <?php else: ?>
                                <a href="<?php echo admin_url('admin.php?page=wc-admin'); ?>" class="button">WooCommerce Settings</a>
                            <?php endif; ?>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <h3>Quick Actions</h3>
            <div class="mojoy-quick-actions">
                <a href="<?php echo home_url(); ?>" class="button button-primary" target="_blank">View Website</a>
                <a href="<?php echo admin_url('customize.php'); ?>" class="button">Customize Theme</a>
                <a href="<?php echo admin_url('nav-menus.php'); ?>" class="button">Setup Menus</a>
                <a href="<?php echo admin_url('widgets.php'); ?>" class="button">Manage Widgets</a>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Impact Dashboard
 */
function mojoy_impact_dashboard() {
    ?>
    <div class="wrap">
        <h1>Mojoy Impact Dashboard</h1>
        
        <div class="mojoy-admin-header">
            <h2>🍽️ Kitchen Initiative Impact</h2>
            <p>Track the positive change your store is making in communities</p>
        </div>
        
        <div class="mojoy-impact-metrics">
            <div class="postbox">
                <h3>This Month's Impact</h3>
                <div class="inside">
                    <?php
                    // Calculate impact based on orders
                    $orders_count = 0;
                    $meals_funded = 0;
                    
                    if (class_exists('WooCommerce')) {
                        $orders = wc_get_orders(array(
                            'limit' => -1,
                            'date_created' => '>=' . date('Y-m-01')
                        ));
                        
                        foreach ($orders as $order) {
                            $orders_count++;
                            $meals_funded += $order->get_item_count() * 2;
                        }
                    }
                    ?>
                    
                    <div class="mojoy-impact-stats">
                        <div class="mojoy-impact-stat">
                            <span class="number"><?php echo $orders_count; ?></span>
                            <span class="label">Orders This Month</span>
                        </div>
                        <div class="mojoy-impact-stat">
                            <span class="number"><?php echo $meals_funded; ?></span>
                            <span class="label">Meals Funded</span>
                        </div>
                        <div class="mojoy-impact-stat">
                            <span class="number"><?php echo ceil($meals_funded / 30); ?></span>
                            <span class="label">Families Helped</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Dashboard Widgets
 */
function mojoy_dashboard_widgets() {
    wp_add_dashboard_widget(
        'mojoy_impact_widget',
        'Mojoy Impact Summary',
        'mojoy_impact_widget_content'
    );
}

/**
 * Impact Widget Content
 */
function mojoy_impact_widget_content() {
    ?>
    <div class="mojoy-widget-content">
        <h4>🌟 Your Impact This Week</h4>
        <?php
        $recent_orders = 0;
        $recent_meals = 0;
        
        if (class_exists('WooCommerce')) {
            $orders = wc_get_orders(array(
                'limit' => -1,
                'date_created' => '>=' . date('Y-m-d', strtotime('-7 days'))
            ));
            
            foreach ($orders as $order) {
                $recent_orders++;
                $recent_meals += $order->get_item_count() * 2;
            }
        }
        ?>
        
        <p><strong><?php echo $recent_orders; ?> orders</strong> placed this week</p>
        <p><strong><?php echo $recent_meals; ?> meals</strong> funded for communities in need</p>
        
        <div style="margin-top: 15px;">
            <a href="<?php echo admin_url('admin.php?page=mojoy-impact'); ?>" class="button button-primary">View Full Impact Dashboard</a>
        </div>
    </div>
    <?php
}

/**
 * Testing and Launch Checklist
 */
function mojoy_launch_checklist() {
    $checklist = array(
        'WooCommerce Setup' => class_exists('WooCommerce'),
        'Sample Products Created' => get_option('mojoy_sample_products_created'),
        'Homepage Content' => get_option('page_on_front'),
        'Menus Configured' => has_nav_menu('primary'),
        'SSL Certificate' => is_ssl(),
        'Permalink Structure' => get_option('permalink_structure'),
        'Search Engine Visibility' => !get_option('blog_public')
    );
    
    return $checklist;
}

/**
 * Display Launch Checklist in Admin
 */
function mojoy_display_launch_checklist() {
    if (current_user_can('manage_options')) {
        $checklist = mojoy_launch_checklist();
        $incomplete_items = array_filter($checklist, function($status) { return !$status; });
        
        if (!empty($incomplete_items)) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>Mojoy Launch Checklist:</strong> ' . count($incomplete_items) . ' items need attention before launch.</p>';
            echo '<ul>';
            foreach ($incomplete_items as $item => $status) {
                echo '<li>❌ ' . $item . '</li>';
            }
            echo '</ul>';
            echo '<p><a href="' . admin_url('admin.php?page=mojoy-settings') . '">View Full Checklist</a></p>';
            echo '</div>';
        }
    }
}
add_action('admin_notices', 'mojoy_display_launch_checklist');

/**
 * Final Integration Check
 */
function mojoy_integration_check() {
    // Verify all components are loaded
    $components = array(
        'Custom Styles' => file_exists(get_template_directory() . '/mojoy-custom-styles.css'),
        'Functions' => function_exists('mojoy_enqueue_custom_styles'),
        'Shop Customizations' => function_exists('mojoy_shop_page_customizations'),
        'Checkout Optimizations' => function_exists('mojoy_checkout_customizations'),
        'Navigation & Footer' => function_exists('mojoy_register_menus'),
        'SEO & Performance' => function_exists('mojoy_seo_optimizations')
    );
    
    $all_loaded = !in_array(false, $components);
    
    if ($all_loaded) {
        update_option('mojoy_integration_complete', true);
    }
    
    return $components;
}

// Run integration check
add_action('admin_init', 'mojoy_integration_check');
