<?php
/**
 * MOJOY Shop & Product Page Customizations
 * Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Customize Shop Page Layout
 */
function mojoy_shop_page_customizations() {
    // Add custom shop header
    add_action('woocommerce_before_shop_loop', 'mojoy_shop_header', 5);
    
    // Add impact message to shop
    add_action('woocommerce_before_shop_loop', 'mojoy_shop_impact_message', 10);
    
    // Customize product loop
    add_action('woocommerce_before_shop_loop_item_title', 'mojoy_product_badges', 5);
    add_action('woocommerce_after_shop_loop_item_title', 'mojoy_product_impact_note', 15);
    
    // Change products per page
    add_filter('loop_shop_per_page', function() { return 12; });
    
    // Add custom sorting options
    add_filter('woocommerce_catalog_orderby', 'mojoy_custom_shop_sorting');
}
add_action('init', 'mojoy_shop_page_customizations');

/**
 * Shop Header with Brand Message
 */
function mojoy_shop_header() {
    if (is_shop()) {
        echo '<div class="mojoy-shop-header">';
        echo '<h1 class="mojoy-shop-title">Premium Comfort Collection</h1>';
        echo '<p class="mojoy-shop-subtitle">Discover clothing that feels as good as it looks—and does good too.</p>';
        echo '<div class="mojoy-shop-pillars">';
        echo '<span class="pillar">✔️ Premium Comfort</span>';
        echo '<span class="pillar">✔️ Timeless Style</span>';
        echo '<span class="pillar">✔️ Purposeful Impact</span>';
        echo '</div>';
        echo '</div>';
    }
}

/**
 * Shop Impact Message
 */
function mojoy_shop_impact_message() {
    if (is_shop()) {
        echo '<div class="mojoy-shop-impact">';
        echo '<div class="impact-content">';
        echo '<h3>🍽️ Every Purchase Makes a Difference</h3>';
        echo '<p>Each item you buy helps fund meals through our Mojoy Kitchen Initiative. Shop with purpose.</p>';
        echo '</div>';
        echo '</div>';
    }
}

/**
 * Product Impact Note
 */
function mojoy_product_impact_note() {
    echo '<div class="mojoy-product-impact-note">';
    echo '<span class="impact-icon">🌟</span>';
    echo '<span class="impact-text">Funds 2 meals per purchase</span>';
    echo '</div>';
}

/**
 * Custom Shop Sorting Options
 */
function mojoy_custom_shop_sorting($options) {
    $options['comfort_rating'] = 'Comfort Rating';
    $options['impact_score'] = 'Impact Score';
    return $options;
}

/**
 * Single Product Page Customizations
 */
function mojoy_single_product_customizations() {
    // Remove default product title and add custom one
    remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_title', 5);
    add_action('woocommerce_single_product_summary', 'mojoy_custom_product_title', 5);
    
    // Add comfort features
    add_action('woocommerce_single_product_summary', 'mojoy_product_comfort_features', 15);
    
    // Add impact message
    add_action('woocommerce_single_product_summary', 'mojoy_single_product_impact', 25);
    
    // Add size guide
    add_action('woocommerce_single_product_summary', 'mojoy_size_guide_link', 35);
    
    // Customize add to cart button
    add_filter('woocommerce_product_single_add_to_cart_text', 'mojoy_custom_add_to_cart_text');
    
    // Add trust badges
    add_action('woocommerce_single_product_summary', 'mojoy_trust_badges', 45);
}
add_action('init', 'mojoy_single_product_customizations');

/**
 * Custom Product Title with Brand Elements
 */
function mojoy_custom_product_title() {
    global $product;
    echo '<div class="mojoy-product-header">';
    echo '<h1 class="mojoy-product-title">' . get_the_title() . '</h1>';
    echo '<div class="mojoy-product-tagline">Premium Comfort • Timeless Style • Purposeful Impact</div>';
    echo '</div>';
}

/**
 * Product Comfort Features
 */
function mojoy_product_comfort_features() {
    echo '<div class="mojoy-comfort-features">';
    echo '<h4>Comfort Features</h4>';
    echo '<ul class="comfort-list">';
    echo '<li><span class="icon">🌬️</span> Ultra-breathable fabric</li>';
    echo '<li><span class="icon">🤗</span> Soft-touch material</li>';
    echo '<li><span class="icon">🏃</span> Movement-friendly design</li>';
    echo '<li><span class="icon">🌱</span> Sustainable materials</li>';
    echo '</ul>';
    echo '</div>';
}

/**
 * Single Product Impact Message
 */
function mojoy_single_product_impact() {
    echo '<div class="mojoy-single-impact">';
    echo '<div class="impact-header">';
    echo '<h4>🍽️ Your Impact</h4>';
    echo '</div>';
    echo '<div class="impact-content">';
    echo '<p><strong>This purchase will fund 2 meals</strong> for underserved communities through our Mojoy Kitchen Initiative.</p>';
    echo '<a href="/about#kitchen-initiative" class="learn-more-link">Learn more about our impact →</a>';
    echo '</div>';
    echo '</div>';
}

/**
 * Size Guide Link
 */
function mojoy_size_guide_link() {
    echo '<div class="mojoy-size-guide">';
    echo '<a href="#" class="size-guide-link" onclick="openSizeGuide()">📏 Size Guide</a>';
    echo '</div>';
}

/**
 * Trust Badges
 */
function mojoy_trust_badges() {
    echo '<div class="mojoy-trust-badges">';
    echo '<div class="trust-badge">';
    echo '<span class="badge-icon">🚚</span>';
    echo '<span class="badge-text">Free Shipping</span>';
    echo '</div>';
    echo '<div class="trust-badge">';
    echo '<span class="badge-icon">↩️</span>';
    echo '<span class="badge-text">Easy Returns</span>';
    echo '</div>';
    echo '<div class="trust-badge">';
    echo '<span class="badge-icon">🛡️</span>';
    echo '<span class="badge-text">Quality Guarantee</span>';
    echo '</div>';
    echo '<div class="trust-badge">';
    echo '<span class="badge-icon">🌍</span>';
    echo '<span class="badge-text">Social Impact</span>';
    echo '</div>';
    echo '</div>';
}

/**
 * Cart Page Customizations
 */
function mojoy_cart_customizations() {
    // Add impact calculator to cart
    add_action('woocommerce_before_cart_table', 'mojoy_cart_impact_calculator', 5);
    
    // Add upsell recommendations
    add_action('woocommerce_cart_collaterals', 'mojoy_cart_upsells', 5);
    
    // Customize proceed to checkout button
    add_filter('woocommerce_order_button_text', 'mojoy_custom_checkout_button_text');
}
add_action('init', 'mojoy_cart_customizations');

/**
 * Cart Impact Calculator
 */
function mojoy_cart_impact_calculator() {
    if (is_cart() && !WC()->cart->is_empty()) {
        $cart_count = WC()->cart->get_cart_contents_count();
        $meals_funded = $cart_count * 2;
        $cart_total = WC()->cart->get_cart_total();
        
        echo '<div class="mojoy-cart-impact-calculator">';
        echo '<div class="impact-header">';
        echo '<h3>🌟 Your Impact Summary</h3>';
        echo '</div>';
        echo '<div class="impact-stats">';
        echo '<div class="stat">';
        echo '<span class="number">' . $meals_funded . '</span>';
        echo '<span class="label">Meals to be funded</span>';
        echo '</div>';
        echo '<div class="stat">';
        echo '<span class="number">' . $cart_count . '</span>';
        echo '<span class="label">Premium items</span>';
        echo '</div>';
        echo '</div>';
        echo '<p class="impact-message">Thank you for choosing comfort with purpose! Your order will help provide ' . $meals_funded . ' nutritious meals to communities in need.</p>';
        echo '</div>';
    }
}

/**
 * Cart Upsells
 */
function mojoy_cart_upsells() {
    echo '<div class="mojoy-cart-upsells">';
    echo '<h3>Complete Your Comfort Collection</h3>';
    echo '<p>Add these premium essentials to maximize your comfort and impact:</p>';
    // This would typically show related products
    echo do_shortcode('[products limit="3" columns="3" category="essentials"]');
    echo '</div>';
}

/**
 * Custom Checkout Button Text
 */
function mojoy_custom_checkout_button_text() {
    return 'Proceed to Checkout & Make Impact';
}

/**
 * Add Custom CSS for Shop & Product Pages
 */
function mojoy_shop_product_styles() {
    ?>
    <style>
    /* Shop Header Styles */
    .mojoy-shop-header {
        text-align: center;
        padding: 40px 0;
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-radius: 12px;
        margin-bottom: 40px;
    }
    
    .mojoy-shop-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--mojoy-primary);
        margin-bottom: 1rem;
    }
    
    .mojoy-shop-subtitle {
        font-size: 1.2rem;
        color: var(--mojoy-text);
        margin-bottom: 2rem;
    }
    
    .mojoy-shop-pillars {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
    }
    
    .mojoy-shop-pillars .pillar {
        background: var(--mojoy-secondary);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    /* Shop Impact Message */
    .mojoy-shop-impact {
        background: linear-gradient(135deg, var(--mojoy-success), #229954);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 40px;
        text-align: center;
    }
    
    .mojoy-shop-impact h3 {
        margin-bottom: 10px;
        font-size: 1.5rem;
    }
    
    /* Product Impact Note */
    .mojoy-product-impact-note {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--mojoy-success);
        font-weight: 600;
        font-size: 0.9rem;
        margin-top: 10px;
    }
    
    /* Single Product Styles */
    .mojoy-product-header {
        margin-bottom: 20px;
    }
    
    .mojoy-product-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--mojoy-primary);
        margin-bottom: 10px;
    }
    
    .mojoy-product-tagline {
        color: var(--mojoy-secondary);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 0.9rem;
    }
    
    /* Comfort Features */
    .mojoy-comfort-features {
        background: var(--mojoy-neutral);
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }
    
    .mojoy-comfort-features h4 {
        margin-bottom: 15px;
        color: var(--mojoy-primary);
    }
    
    .comfort-list {
        list-style: none;
        padding: 0;
    }
    
    .comfort-list li {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
    }
    
    /* Single Product Impact */
    .mojoy-single-impact {
        background: linear-gradient(135deg, var(--mojoy-success), #229954);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }
    
    .mojoy-single-impact h4 {
        margin-bottom: 10px;
    }
    
    .learn-more-link {
        color: white;
        text-decoration: underline;
        font-weight: 600;
    }
    
    /* Trust Badges */
    .mojoy-trust-badges {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin: 20px 0;
    }
    
    .trust-badge {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px;
        background: var(--mojoy-neutral);
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    /* Cart Impact Calculator */
    .mojoy-cart-impact-calculator {
        background: linear-gradient(135deg, var(--mojoy-success), #229954);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .impact-stats {
        display: flex;
        justify-content: center;
        gap: 3rem;
        margin: 20px 0;
    }
    
    .impact-stats .stat {
        text-align: center;
    }
    
    .impact-stats .number {
        display: block;
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .impact-stats .label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .mojoy-shop-pillars {
            gap: 1rem;
        }
        
        .mojoy-trust-badges {
            grid-template-columns: 1fr;
        }
        
        .impact-stats {
            flex-direction: column;
            gap: 1rem;
        }
    }
    </style>
    <?php
}
add_action('wp_head', 'mojoy_shop_product_styles');
