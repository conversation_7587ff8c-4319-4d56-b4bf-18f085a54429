<?php
/**
 * MOJOY Navigation & Footer Customizations
 * Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Setup Navigation Menus
 */
function mojoy_register_menus() {
    register_nav_menus(array(
        'primary' => 'Primary Navigation',
        'footer-main' => 'Footer Main Menu',
        'footer-support' => 'Footer Support Menu',
        'footer-company' => 'Footer Company Menu',
        'mobile' => 'Mobile Menu'
    ));
}
add_action('init', 'mojoy_register_menus');

/**
 * Customize Header Navigation
 */
function mojoy_header_customizations() {
    // Add impact message to header
    add_action('kadence_header', 'mojoy_header_impact_banner', 5);
    
    // Add cart icon with impact counter
    add_action('kadence_header', 'mojoy_header_cart_impact', 25);
}
add_action('init', 'mojoy_header_customizations');

/**
 * Header Impact Banner
 */
function mojoy_header_impact_banner() {
    echo '<div class="mojoy-header-impact-banner">';
    echo '<div class="impact-banner-content">';
    echo '<span class="impact-text">🍽️ Every purchase funds meals for communities in need</span>';
    echo '<a href="/about#kitchen-initiative" class="learn-more">Learn More</a>';
    echo '</div>';
    echo '</div>';
}

/**
 * Header Cart with Impact Counter
 */
function mojoy_header_cart_impact() {
    if (class_exists('WooCommerce')) {
        $cart_count = WC()->cart->get_cart_contents_count();
        $meals_funded = $cart_count * 2;
        
        echo '<div class="mojoy-header-cart">';
        echo '<a href="' . wc_get_cart_url() . '" class="cart-link">';
        echo '<span class="cart-icon">🛒</span>';
        echo '<span class="cart-count">' . $cart_count . '</span>';
        if ($cart_count > 0) {
            echo '<span class="cart-impact">(' . $meals_funded . ' meals)</span>';
        }
        echo '</a>';
        echo '</div>';
    }
}

/**
 * Footer Customizations
 */
function mojoy_footer_customizations() {
    // Remove default footer
    remove_action('kadence_footer', 'kadence_footer_markup');
    
    // Add custom footer
    add_action('kadence_footer', 'mojoy_custom_footer');
}
add_action('init', 'mojoy_footer_customizations');

/**
 * Custom Footer
 */
function mojoy_custom_footer() {
    ?>
    <footer class="mojoy-footer">
        <div class="footer-main">
            <div class="mojoy-container">
                <div class="footer-content">
                    
                    <!-- Brand Section -->
                    <div class="footer-section footer-brand">
                        <div class="brand-logo">
                            <h3>MOJOY</h3>
                            <p class="brand-tagline">Feel Good, Do Good</p>
                        </div>
                        <p class="brand-description">Premium comfort apparel with purposeful impact. Every purchase helps fund meals for underserved communities through our Kitchen Initiative.</p>
                        <div class="impact-stats">
                            <div class="stat">
                                <span class="number">15,000+</span>
                                <span class="label">Meals Funded</span>
                            </div>
                            <div class="stat">
                                <span class="number">35+</span>
                                <span class="label">Communities</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Shop Section -->
                    <div class="footer-section footer-shop">
                        <h4>Shop</h4>
                        <ul class="footer-menu">
                            <li><a href="/shop">All Products</a></li>
                            <li><a href="/shop/t-shirts">T-Shirts</a></li>
                            <li><a href="/shop/polos">Polos</a></li>
                            <li><a href="/shop/essentials">Essentials</a></li>
                            <li><a href="/shop/new-arrivals">New Arrivals</a></li>
                            <li><a href="/shop/bestsellers">Bestsellers</a></li>
                        </ul>
                    </div>
                    
                    <!-- Company Section -->
                    <div class="footer-section footer-company">
                        <h4>Company</h4>
                        <ul class="footer-menu">
                            <li><a href="/about">Our Story</a></li>
                            <li><a href="/about#kitchen-initiative">Kitchen Initiative</a></li>
                            <li><a href="/sustainability">Sustainability</a></li>
                            <li><a href="/careers">Careers</a></li>
                            <li><a href="/press">Press</a></li>
                            <li><a href="/contact">Contact</a></li>
                        </ul>
                    </div>
                    
                    <!-- Support Section -->
                    <div class="footer-section footer-support">
                        <h4>Support</h4>
                        <ul class="footer-menu">
                            <li><a href="/faq">FAQ</a></li>
                            <li><a href="/shipping-returns">Shipping & Returns</a></li>
                            <li><a href="/size-guide">Size Guide</a></li>
                            <li><a href="/care-instructions">Care Instructions</a></li>
                            <li><a href="/track-order">Track Order</a></li>
                            <li><a href="/contact">Customer Support</a></li>
                        </ul>
                    </div>
                    
                    <!-- Newsletter Section -->
                    <div class="footer-section footer-newsletter">
                        <h4>Stay Connected</h4>
                        <p>Get updates on new products, impact stories, and exclusive offers</p>
                        <form class="newsletter-form" action="#" method="post">
                            <div class="form-group">
                                <input type="email" name="email" placeholder="Enter your email" required>
                                <button type="submit" class="subscribe-btn">Subscribe</button>
                            </div>
                        </form>
                        <div class="social-links">
                            <a href="https://instagram.com/mojoy" target="_blank" class="social-link instagram">📷</a>
                            <a href="https://facebook.com/mojoy" target="_blank" class="social-link facebook">📘</a>
                            <a href="https://twitter.com/mojoy" target="_blank" class="social-link twitter">🐦</a>
                            <a href="https://linkedin.com/company/mojoy" target="_blank" class="social-link linkedin">💼</a>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        
        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <div class="mojoy-container">
                <div class="footer-bottom-content">
                    <div class="footer-legal">
                        <p>&copy; <?php echo date('Y'); ?> Mojoy. All rights reserved.</p>
                        <div class="legal-links">
                            <a href="/privacy-policy">Privacy Policy</a>
                            <a href="/terms-of-service">Terms of Service</a>
                            <a href="/cookie-policy">Cookie Policy</a>
                        </div>
                    </div>
                    <div class="footer-certifications">
                        <span class="cert">🌱 Sustainable</span>
                        <span class="cert">🛡️ Secure</span>
                        <span class="cert">🍽️ Impact Driven</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <?php
}

/**
 * Mobile Menu Customizations
 */
function mojoy_mobile_menu_customizations() {
    add_action('wp_footer', 'mojoy_mobile_menu_script');
}
add_action('init', 'mojoy_mobile_menu_customizations');

/**
 * Mobile Menu Script
 */
function mojoy_mobile_menu_script() {
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileMenuToggle && mobileMenu) {
            mobileMenuToggle.addEventListener('click', function() {
                mobileMenu.classList.toggle('active');
                document.body.classList.toggle('mobile-menu-open');
            });
        }
        
        // Cart update with impact
        function updateCartImpact() {
            const cartCount = document.querySelector('.cart-count');
            const cartImpact = document.querySelector('.cart-impact');
            
            if (cartCount && cartImpact) {
                const count = parseInt(cartCount.textContent) || 0;
                const meals = count * 2;
                cartImpact.textContent = count > 0 ? `(${meals} meals)` : '';
            }
        }
        
        // Update cart impact on page load
        updateCartImpact();
        
        // Newsletter form submission
        const newsletterForm = document.querySelector('.newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;
                
                // Here you would typically send the email to your newsletter service
                alert('Thank you for subscribing! You\'ll receive updates about our impact and new products.');
                this.reset();
            });
        }
    });
    </script>
    <?php
}

/**
 * Add Navigation & Footer Styles
 */
function mojoy_navigation_footer_styles() {
    ?>
    <style>
    /* Header Impact Banner */
    .mojoy-header-impact-banner {
        background: linear-gradient(135deg, var(--mojoy-success), #229954);
        color: white;
        padding: 8px 0;
        text-align: center;
        font-size: 0.9rem;
    }
    
    .impact-banner-content {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
    
    .impact-banner-content .learn-more {
        color: white;
        text-decoration: underline;
        font-weight: 600;
    }
    
    /* Header Cart */
    .mojoy-header-cart {
        position: relative;
    }
    
    .cart-link {
        display: flex;
        align-items: center;
        gap: 5px;
        text-decoration: none;
        color: var(--mojoy-primary);
        font-weight: 600;
    }
    
    .cart-icon {
        font-size: 1.2rem;
    }
    
    .cart-count {
        background: var(--mojoy-secondary);
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 700;
    }
    
    .cart-impact {
        font-size: 0.8rem;
        color: var(--mojoy-success);
        font-weight: 600;
    }
    
    /* Footer Styles */
    .mojoy-footer {
        background: var(--mojoy-primary);
        color: white;
        margin-top: 60px;
    }
    
    .footer-main {
        padding: 60px 0 40px;
    }
    
    .footer-content {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
        gap: 3rem;
    }
    
    .footer-section h4 {
        color: white;
        margin-bottom: 1.5rem;
        font-size: 1.2rem;
        font-weight: 600;
    }
    
    .footer-brand .brand-logo h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .brand-tagline {
        color: var(--mojoy-accent);
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .brand-description {
        line-height: 1.6;
        margin-bottom: 2rem;
        opacity: 0.9;
    }
    
    .impact-stats {
        display: flex;
        gap: 2rem;
    }
    
    .impact-stats .stat {
        text-align: center;
    }
    
    .impact-stats .number {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--mojoy-accent);
    }
    
    .impact-stats .label {
        font-size: 0.8rem;
        opacity: 0.8;
    }
    
    .footer-menu {
        list-style: none;
        padding: 0;
    }
    
    .footer-menu li {
        margin-bottom: 0.8rem;
    }
    
    .footer-menu a {
        color: white;
        text-decoration: none;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }
    
    .footer-menu a:hover {
        opacity: 1;
        color: var(--mojoy-accent);
    }
    
    .newsletter-form {
        margin-bottom: 2rem;
    }
    
    .newsletter-form .form-group {
        display: flex;
        gap: 0.5rem;
    }
    
    .newsletter-form input {
        flex: 1;
        padding: 12px;
        border: none;
        border-radius: 6px;
        background: rgba(255,255,255,0.1);
        color: white;
        border: 2px solid transparent;
    }
    
    .newsletter-form input::placeholder {
        color: rgba(255,255,255,0.7);
    }
    
    .newsletter-form input:focus {
        outline: none;
        border-color: var(--mojoy-accent);
        background: rgba(255,255,255,0.15);
    }
    
    .subscribe-btn {
        padding: 12px 20px;
        background: var(--mojoy-secondary);
        color: white;
        border: none;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .subscribe-btn:hover {
        background: #c0392b;
    }
    
    .social-links {
        display: flex;
        gap: 1rem;
    }
    
    .social-link {
        display: inline-block;
        width: 40px;
        height: 40px;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        text-align: center;
        line-height: 40px;
        text-decoration: none;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }
    
    .social-link:hover {
        background: var(--mojoy-accent);
        transform: translateY(-2px);
    }
    
    /* Footer Bottom */
    .footer-bottom {
        border-top: 1px solid rgba(255,255,255,0.1);
        padding: 20px 0;
    }
    
    .footer-bottom-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .legal-links {
        display: flex;
        gap: 2rem;
        margin-top: 0.5rem;
    }
    
    .legal-links a {
        color: white;
        text-decoration: none;
        opacity: 0.7;
        font-size: 0.9rem;
    }
    
    .legal-links a:hover {
        opacity: 1;
    }
    
    .footer-certifications {
        display: flex;
        gap: 1rem;
    }
    
    .footer-certifications .cert {
        background: rgba(255,255,255,0.1);
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .impact-banner-content {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .footer-content {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .footer-bottom-content {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
        
        .legal-links {
            justify-content: center;
        }
        
        .impact-stats {
            justify-content: center;
        }
    }
    </style>
    <?php
}
add_action('wp_head', 'mojoy_navigation_footer_styles');
