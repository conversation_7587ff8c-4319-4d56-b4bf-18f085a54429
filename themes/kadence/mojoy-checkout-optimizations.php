<?php
/**
 * MOJOY Checkout & Cart Experience Optimizations
 * Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Checkout Page Customizations
 */
function mojoy_checkout_customizations() {
    // Add impact summary to checkout
    add_action('woocommerce_checkout_before_customer_details', 'mojoy_checkout_impact_summary', 5);
    
    // Add trust elements
    add_action('woocommerce_checkout_before_customer_details', 'mojoy_checkout_trust_elements', 10);
    
    // Customize checkout fields
    add_filter('woocommerce_checkout_fields', 'mojoy_customize_checkout_fields');
    
    // Add order notes with impact message
    add_action('woocommerce_checkout_after_order_review', 'mojoy_checkout_impact_reminder', 5);
    
    // Customize place order button
    add_filter('woocommerce_order_button_text', 'mojoy_custom_place_order_text');
}
add_action('init', 'mojoy_checkout_customizations');

/**
 * Checkout Impact Summary
 */
function mojoy_checkout_impact_summary() {
    if (is_checkout()) {
        $cart_count = WC()->cart->get_cart_contents_count();
        $meals_funded = $cart_count * 2;
        $cart_total = WC()->cart->get_cart_total();
        
        echo '<div class="mojoy-checkout-impact-summary">';
        echo '<div class="impact-header">';
        echo '<h3>🌟 Your Order Impact</h3>';
        echo '</div>';
        echo '<div class="impact-details">';
        echo '<div class="impact-row">';
        echo '<span class="label">Items in cart:</span>';
        echo '<span class="value">' . $cart_count . ' premium pieces</span>';
        echo '</div>';
        echo '<div class="impact-row">';
        echo '<span class="label">Meals to be funded:</span>';
        echo '<span class="value highlight">' . $meals_funded . ' nutritious meals</span>';
        echo '</div>';
        echo '<div class="impact-row">';
        echo '<span class="label">Communities helped:</span>';
        echo '<span class="value">Underserved families across India</span>';
        echo '</div>';
        echo '</div>';
        echo '<div class="impact-message">';
        echo '<p>🍽️ <strong>Thank you for choosing comfort with purpose!</strong> Your order will help provide ' . $meals_funded . ' meals through our Mojoy Kitchen Initiative.</p>';
        echo '</div>';
        echo '</div>';
    }
}

/**
 * Checkout Trust Elements
 */
function mojoy_checkout_trust_elements() {
    echo '<div class="mojoy-checkout-trust">';
    echo '<div class="trust-header">';
    echo '<h4>🛡️ Secure & Trusted Checkout</h4>';
    echo '</div>';
    echo '<div class="trust-badges">';
    echo '<div class="trust-item">';
    echo '<span class="icon">🔒</span>';
    echo '<span class="text">SSL Encrypted</span>';
    echo '</div>';
    echo '<div class="trust-item">';
    echo '<span class="icon">💳</span>';
    echo '<span class="text">Secure Payments</span>';
    echo '</div>';
    echo '<div class="trust-item">';
    echo '<span class="icon">🚚</span>';
    echo '<span class="text">Free Shipping</span>';
    echo '</div>';
    echo '<div class="trust-item">';
    echo '<span class="icon">↩️</span>';
    echo '<span class="text">Easy Returns</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Customize Checkout Fields
 */
function mojoy_customize_checkout_fields($fields) {
    // Add custom field for impact motivation
    $fields['billing']['billing_impact_motivation'] = array(
        'type' => 'textarea',
        'label' => 'What inspired you to join the Mojoy movement? (Optional)',
        'placeholder' => 'Share your story with us...',
        'required' => false,
        'class' => array('form-row-wide', 'mojoy-impact-field'),
        'priority' => 999,
    );
    
    // Customize existing fields
    $fields['billing']['billing_first_name']['placeholder'] = 'First Name';
    $fields['billing']['billing_last_name']['placeholder'] = 'Last Name';
    $fields['billing']['billing_email']['placeholder'] = 'Email Address';
    $fields['billing']['billing_phone']['placeholder'] = 'Phone Number';
    
    return $fields;
}

/**
 * Checkout Impact Reminder
 */
function mojoy_checkout_impact_reminder() {
    echo '<div class="mojoy-checkout-reminder">';
    echo '<div class="reminder-content">';
    echo '<h4>🌱 One More Thing...</h4>';
    echo '<p>By completing this order, you\'re not just getting premium comfort clothing—you\'re joining a movement that believes in doing good while feeling good.</p>';
    echo '<div class="reminder-stats">';
    echo '<span class="stat">15,000+ meals funded</span>';
    echo '<span class="stat">35+ communities served</span>';
    echo '<span class="stat">8,500+ happy customers</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Custom Place Order Button Text
 */
function mojoy_custom_place_order_text() {
    return 'Complete Order & Make Impact 🌟';
}

/**
 * Order Confirmation Customizations
 */
function mojoy_order_confirmation_customizations() {
    // Add impact celebration to thank you page
    add_action('woocommerce_thankyou', 'mojoy_order_impact_celebration', 5);
    
    // Add social sharing
    add_action('woocommerce_thankyou', 'mojoy_order_social_sharing', 15);
    
    // Add next steps
    add_action('woocommerce_thankyou', 'mojoy_order_next_steps', 25);
}
add_action('init', 'mojoy_order_confirmation_customizations');

/**
 * Order Impact Celebration
 */
function mojoy_order_impact_celebration($order_id) {
    if (!$order_id) return;
    
    $order = wc_get_order($order_id);
    $item_count = $order->get_item_count();
    $meals_funded = $item_count * 2;
    
    echo '<div class="mojoy-impact-celebration">';
    echo '<div class="celebration-header">';
    echo '<h2>🎉 Thank You for Making a Difference!</h2>';
    echo '</div>';
    echo '<div class="celebration-content">';
    echo '<div class="impact-achievement">';
    echo '<div class="achievement-icon">🍽️</div>';
    echo '<div class="achievement-text">';
    echo '<h3>Your order will fund ' . $meals_funded . ' meals!</h3>';
    echo '<p>Through the Mojoy Kitchen Initiative, your purchase will help provide nutritious meals to underserved communities across India.</p>';
    echo '</div>';
    echo '</div>';
    echo '<div class="celebration-stats">';
    echo '<div class="stat-item">';
    echo '<span class="number">' . $item_count . '</span>';
    echo '<span class="label">Premium Items Ordered</span>';
    echo '</div>';
    echo '<div class="stat-item">';
    echo '<span class="number">' . $meals_funded . '</span>';
    echo '<span class="label">Meals to be Funded</span>';
    echo '</div>';
    echo '<div class="stat-item">';
    echo '<span class="number">1</span>';
    echo '<span class="label">Community Member Helped</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Order Social Sharing
 */
function mojoy_order_social_sharing($order_id) {
    if (!$order_id) return;
    
    $order = wc_get_order($order_id);
    $item_count = $order->get_item_count();
    $meals_funded = $item_count * 2;
    
    $share_text = "I just ordered from @Mojoy and my purchase will fund " . $meals_funded . " meals for communities in need! 🍽️ #FeelGoodDoGood #MojoyImpact";
    $share_url = urlencode(home_url());
    
    echo '<div class="mojoy-social-sharing">';
    echo '<h3>Share Your Impact</h3>';
    echo '<p>Let your friends know about the positive change you\'re making!</p>';
    echo '<div class="share-buttons">';
    echo '<a href="https://twitter.com/intent/tweet?text=' . urlencode($share_text) . '&url=' . $share_url . '" target="_blank" class="share-btn twitter">Share on Twitter</a>';
    echo '<a href="https://www.facebook.com/sharer/sharer.php?u=' . $share_url . '" target="_blank" class="share-btn facebook">Share on Facebook</a>';
    echo '<a href="https://www.instagram.com/" target="_blank" class="share-btn instagram">Share on Instagram</a>';
    echo '</div>';
    echo '</div>';
}

/**
 * Order Next Steps
 */
function mojoy_order_next_steps($order_id) {
    echo '<div class="mojoy-next-steps">';
    echo '<h3>What Happens Next?</h3>';
    echo '<div class="steps-timeline">';
    echo '<div class="step">';
    echo '<div class="step-icon">📦</div>';
    echo '<div class="step-content">';
    echo '<h4>Order Processing</h4>';
    echo '<p>We\'ll carefully prepare your premium comfort items with love and attention to detail.</p>';
    echo '</div>';
    echo '</div>';
    echo '<div class="step">';
    echo '<div class="step-icon">🚚</div>';
    echo '<div class="step-content">';
    echo '<h4>Free Shipping</h4>';
    echo '<p>Your order will be shipped within 2-3 business days with free delivery to your doorstep.</p>';
    echo '</div>';
    echo '</div>';
    echo '<div class="step">';
    echo '<div class="step-icon">🍽️</div>';
    echo '<div class="step-content">';
    echo '<h4>Impact in Action</h4>';
    echo '<p>We\'ll allocate funds from your purchase to provide meals through our Kitchen Initiative.</p>';
    echo '</div>';
    echo '</div>';
    echo '<div class="step">';
    echo '<div class="step-icon">📧</div>';
    echo '<div class="step-content">';
    echo '<h4>Impact Updates</h4>';
    echo '<p>You\'ll receive updates about the communities your purchase has helped support.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Add Custom CSS for Checkout & Order Pages
 */
function mojoy_checkout_styles() {
    ?>
    <style>
    /* Checkout Impact Summary */
    .mojoy-checkout-impact-summary {
        background: linear-gradient(135deg, var(--mojoy-success), #229954);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
    }
    
    .mojoy-checkout-impact-summary h3 {
        margin-bottom: 20px;
        font-size: 1.5rem;
    }
    
    .impact-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }
    
    .impact-row:last-child {
        border-bottom: none;
    }
    
    .impact-row .highlight {
        font-weight: 700;
        font-size: 1.1rem;
    }
    
    .impact-message {
        background: rgba(255,255,255,0.1);
        padding: 15px;
        border-radius: 8px;
        margin-top: 20px;
    }
    
    /* Checkout Trust Elements */
    .mojoy-checkout-trust {
        background: var(--mojoy-neutral);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
    }
    
    .mojoy-checkout-trust h4 {
        margin-bottom: 15px;
        color: var(--mojoy-primary);
    }
    
    .trust-badges {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .trust-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    /* Checkout Reminder */
    .mojoy-checkout-reminder {
        background: var(--mojoy-neutral);
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
    }
    
    .reminder-stats {
        display: flex;
        gap: 1rem;
        margin-top: 15px;
        flex-wrap: wrap;
    }
    
    .reminder-stats .stat {
        background: var(--mojoy-secondary);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    /* Impact Celebration */
    .mojoy-impact-celebration {
        background: linear-gradient(135deg, var(--mojoy-success), #229954);
        color: white;
        padding: 40px;
        border-radius: 12px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .celebration-header h2 {
        font-size: 2.5rem;
        margin-bottom: 20px;
    }
    
    .impact-achievement {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 30px;
        text-align: left;
    }
    
    .achievement-icon {
        font-size: 4rem;
    }
    
    .achievement-text h3 {
        font-size: 1.8rem;
        margin-bottom: 10px;
    }
    
    .celebration-stats {
        display: flex;
        justify-content: center;
        gap: 3rem;
        margin-top: 30px;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-item .number {
        display: block;
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .stat-item .label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    /* Social Sharing */
    .mojoy-social-sharing {
        background: var(--mojoy-neutral);
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .share-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
        flex-wrap: wrap;
    }
    
    .share-btn {
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: transform 0.3s ease;
    }
    
    .share-btn:hover {
        transform: translateY(-2px);
    }
    
    .share-btn.twitter {
        background: #1DA1F2;
        color: white;
    }
    
    .share-btn.facebook {
        background: #4267B2;
        color: white;
    }
    
    .share-btn.instagram {
        background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
        color: white;
    }
    
    /* Next Steps Timeline */
    .mojoy-next-steps {
        background: white;
        padding: 30px;
        border-radius: 12px;
        border: 2px solid var(--mojoy-border);
    }
    
    .steps-timeline {
        display: grid;
        gap: 20px;
        margin-top: 20px;
    }
    
    .step {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 20px;
        background: var(--mojoy-neutral);
        border-radius: 8px;
    }
    
    .step-icon {
        font-size: 2rem;
        flex-shrink: 0;
    }
    
    .step-content h4 {
        margin-bottom: 8px;
        color: var(--mojoy-primary);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .trust-badges {
            grid-template-columns: 1fr;
        }
        
        .celebration-stats {
            flex-direction: column;
            gap: 1rem;
        }
        
        .impact-achievement {
            flex-direction: column;
            text-align: center;
        }
        
        .share-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
    </style>
    <?php
}
add_action('wp_head', 'mojoy_checkout_styles');
