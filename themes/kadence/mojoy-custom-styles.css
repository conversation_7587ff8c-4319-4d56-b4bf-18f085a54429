/*
===========================================
MOJOY BRAND CUSTOM STYLES
Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
===========================================
*/

/* BRAND COLORS - OPTIMIZED FOR CONVERSION */
:root {
    --mojoy-primary: #1a252f;      /* Deep Navy - Premium & Trust */
    --mojoy-secondary: #e74c3c;    /* Warm Red - Impact & Energy */
    --mojoy-accent: #f39c12;       /* Golden Yellow - Joy & Optimism */
    --mojoy-neutral: #f8f9fa;      /* Light Gray - Clean & Minimal */
    --mojoy-dark: #2c3e50;         /* Charcoal - Sophistication */
    --mojoy-light: #ffffff;        /* Pure White - Clarity */
    --mojoy-success: #27ae60;      /* Green - Impact & Growth */
    --mojoy-text: #333333;         /* Dark Gray - Readability */
    --mojoy-border: #e9ecef;       /* Light Border */
    --mojoy-shadow: rgba(0,0,0,0.1); /* Subtle Shadow */
}

/* TYPOGRAPHY - PREMIUM & READABLE */
.mojoy-hero-title {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 700;
    font-size: clamp(2.5rem, 5vw, 4rem);
    line-height: 1.2;
    color: var(--mojoy-primary);
    letter-spacing: -0.02em;
}

.mojoy-tagline {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    color: var(--mojoy-dark);
    line-height: 1.6;
    margin-top: 1rem;
}

.mojoy-impact-text {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    color: var(--mojoy-secondary);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* BUTTONS - CONVERSION OPTIMIZED */
.mojoy-btn-primary {
    background: linear-gradient(135deg, var(--mojoy-secondary), #c0392b);
    color: var(--mojoy-light);
    padding: 16px 32px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mojoy-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    background: linear-gradient(135deg, #c0392b, var(--mojoy-secondary));
}

.mojoy-btn-secondary {
    background: transparent;
    color: var(--mojoy-primary);
    padding: 16px 32px;
    border: 2px solid var(--mojoy-primary);
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mojoy-btn-secondary:hover {
    background: var(--mojoy-primary);
    color: var(--mojoy-light);
    transform: translateY(-2px);
}

/* SECTIONS - CLEAN & SPACIOUS */
.mojoy-section {
    padding: 80px 0;
}

.mojoy-section-alt {
    background: var(--mojoy-neutral);
    padding: 80px 0;
}

/* IMPACT HIGHLIGHT BOXES */
.mojoy-impact-box {
    background: linear-gradient(135deg, var(--mojoy-success), #229954);
    color: var(--mojoy-light);
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(39, 174, 96, 0.2);
}

.mojoy-impact-number {
    font-size: 3rem;
    font-weight: 700;
    display: block;
    margin-bottom: 10px;
}

.mojoy-impact-label {
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.9;
}

/* PRODUCT CARDS - PREMIUM FEEL */
.mojoy-product-card {
    background: var(--mojoy-light);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.mojoy-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.mojoy-product-badge {
    background: var(--mojoy-accent);
    color: var(--mojoy-light);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

/* TESTIMONIALS */
.mojoy-testimonial {
    background: var(--mojoy-light);
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border-left: 4px solid var(--mojoy-secondary);
}

.mojoy-testimonial-text {
    font-style: italic;
    font-size: 1.2rem;
    line-height: 1.6;
    color: var(--mojoy-dark);
    margin-bottom: 20px;
}

.mojoy-testimonial-author {
    font-weight: 600;
    color: var(--mojoy-primary);
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
    .mojoy-section,
    .mojoy-section-alt {
        padding: 60px 0;
    }
    
    .mojoy-btn-primary,
    .mojoy-btn-secondary {
        padding: 14px 24px;
        font-size: 1rem;
    }
    
    .mojoy-impact-box {
        padding: 30px 20px;
    }
    
    .mojoy-impact-number {
        font-size: 2.5rem;
    }
}

/* WOOCOMMERCE INTEGRATION */
.woocommerce .mojoy-product-card .woocommerce-loop-product__title {
    color: var(--mojoy-primary);
    font-weight: 600;
    margin: 15px 0 10px;
}

.woocommerce .mojoy-product-card .price {
    color: var(--mojoy-secondary);
    font-weight: 700;
    font-size: 1.2rem;
}

.woocommerce .single-product .summary .price {
    color: var(--mojoy-secondary);
    font-size: 1.5rem;
    font-weight: 700;
}

/* HEADER CUSTOMIZATION */
.site-header {
    background: var(--mojoy-light);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.main-navigation a {
    color: var(--mojoy-primary);
    font-weight: 500;
    transition: color 0.3s ease;
}

.main-navigation a:hover {
    color: var(--mojoy-secondary);
}

/* FOOTER STYLING */
.site-footer {
    background: var(--mojoy-primary);
    color: var(--mojoy-light);
    padding: 60px 0 30px;
}

.site-footer a {
    color: var(--mojoy-light);
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.site-footer a:hover {
    opacity: 1;
    color: var(--mojoy-accent);
}

/* CRITICAL FIXES - REMOVE GREEN BACKGROUNDS */
body, html {
    background-color: var(--mojoy-light) !important;
    background: var(--mojoy-light) !important;
}

.site-main, .content-area, .site-content {
    background-color: var(--mojoy-light) !important;
    background: var(--mojoy-light) !important;
}

/* Fix any green backgrounds that might be applied */
.wp-block-group, .wp-block-cover, .wp-block-columns {
    background-color: transparent !important;
}

/* Ensure proper page backgrounds */
.page, .single, .archive, .home {
    background-color: var(--mojoy-light) !important;
}

/* Fix header and footer backgrounds */
.site-header {
    background-color: var(--mojoy-light) !important;
}

.site-footer {
    background-color: var(--mojoy-primary) !important;
}

/* UTILITY CLASSES */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

/* ANIMATION CLASSES */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}
