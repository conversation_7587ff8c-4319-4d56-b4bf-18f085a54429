<?php
/**
 * MOJOY SEO & Performance Optimizations
 * Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SEO Meta Tags and Structured Data
 */
function mojoy_seo_optimizations() {
    add_action('wp_head', 'mojoy_meta_tags');
    add_action('wp_head', 'mojoy_structured_data');
    add_action('wp_head', 'mojoy_open_graph_tags');
    add_filter('document_title_parts', 'mojoy_custom_title_parts');
}
add_action('init', 'mojoy_seo_optimizations');

/**
 * Custom Meta Tags
 */
function mojoy_meta_tags() {
    global $post;
    
    // Default meta description
    $meta_description = 'Mojoy - Premium comfort apparel with purposeful impact. Feel Good, Do Good. Every purchase funds meals for underserved communities.';
    
    // Page-specific meta descriptions
    if (is_front_page()) {
        $meta_description = 'Discover Mojoy\'s premium comfort clothing collection. Timeless style, breathable fabrics, and purposeful impact. Every purchase funds meals through our Kitchen Initiative.';
    } elseif (is_shop()) {
        $meta_description = 'Shop Mojoy\'s premium comfort collection. Ultra-soft t-shirts, breathable polos, and lifestyle essentials. Free shipping and 30-day returns.';
    } elseif (is_product()) {
        $product = wc_get_product();
        if ($product) {
            $meta_description = wp_trim_words($product->get_short_description(), 25) . ' Premium comfort, timeless style, purposeful impact.';
        }
    } elseif (is_page('about')) {
        $meta_description = 'Learn about Mojoy\'s mission to combine premium comfort with purposeful impact. Discover our Kitchen Initiative and how every purchase makes a difference.';
    }
    
    echo '<meta name="description" content="' . esc_attr($meta_description) . '">' . "\n";
    echo '<meta name="keywords" content="premium comfort clothing, sustainable apparel, social impact fashion, breathable t-shirts, organic cotton, ethical fashion, India">' . "\n";
    echo '<meta name="author" content="Mojoy">' . "\n";
    echo '<meta name="robots" content="index, follow">' . "\n";
    
    // Viewport and mobile optimization
    echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">' . "\n";
    echo '<meta name="theme-color" content="#1a252f">' . "\n";
    
    // Performance hints
    echo '<link rel="preconnect" href="https://fonts.googleapis.com">' . "\n";
    echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n";
    echo '<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">' . "\n";
}

/**
 * Structured Data for SEO
 */
function mojoy_structured_data() {
    global $post;
    
    if (is_front_page()) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Mojoy',
            'alternateName' => 'Mojoy Apparel',
            'description' => 'Premium comfort apparel with purposeful impact. Feel Good, Do Good.',
            'url' => home_url(),
            'logo' => array(
                '@type' => 'ImageObject',
                'url' => get_template_directory_uri() . '/assets/images/mojoy-logo.png'
            ),
            'contactPoint' => array(
                '@type' => 'ContactPoint',
                'telephone' => '+91-98765-43210',
                'contactType' => 'Customer Service',
                'email' => '<EMAIL>'
            ),
            'sameAs' => array(
                'https://instagram.com/mojoy',
                'https://facebook.com/mojoy',
                'https://twitter.com/mojoy',
                'https://linkedin.com/company/mojoy'
            ),
            'foundingDate' => '2023',
            'founders' => array(
                '@type' => 'Person',
                'name' => 'Mojoy Founders'
            ),
            'mission' => 'To bring effortless comfort and timeless style to everyday life while nourishing communities in need.'
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
    
    if (is_product()) {
        global $product;
        if ($product) {
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'Product',
                'name' => $product->get_name(),
                'description' => wp_strip_all_tags($product->get_description()),
                'sku' => $product->get_sku(),
                'brand' => array(
                    '@type' => 'Brand',
                    'name' => 'Mojoy'
                ),
                'offers' => array(
                    '@type' => 'Offer',
                    'price' => $product->get_price(),
                    'priceCurrency' => 'INR',
                    'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                    'seller' => array(
                        '@type' => 'Organization',
                        'name' => 'Mojoy'
                    )
                ),
                'aggregateRating' => array(
                    '@type' => 'AggregateRating',
                    'ratingValue' => '4.8',
                    'reviewCount' => '150'
                )
            );
            
            echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
        }
    }
}

/**
 * Open Graph Tags for Social Media
 */
function mojoy_open_graph_tags() {
    global $post;
    
    $og_title = get_the_title();
    $og_description = 'Premium comfort apparel with purposeful impact. Feel Good, Do Good.';
    $og_image = get_template_directory_uri() . '/assets/images/mojoy-og-image.jpg';
    $og_url = get_permalink();
    
    if (is_front_page()) {
        $og_title = 'Mojoy - Feel Good, Do Good | Premium Comfort Apparel';
        $og_description = 'Discover premium comfort clothing that makes a difference. Every purchase funds meals for underserved communities.';
    } elseif (is_product()) {
        $product = wc_get_product();
        if ($product) {
            $og_description = wp_trim_words($product->get_short_description(), 25);
            $product_image = wp_get_attachment_image_src(get_post_thumbnail_id(), 'large');
            if ($product_image) {
                $og_image = $product_image[0];
            }
        }
    }
    
    echo '<meta property="og:title" content="' . esc_attr($og_title) . '">' . "\n";
    echo '<meta property="og:description" content="' . esc_attr($og_description) . '">' . "\n";
    echo '<meta property="og:image" content="' . esc_url($og_image) . '">' . "\n";
    echo '<meta property="og:url" content="' . esc_url($og_url) . '">' . "\n";
    echo '<meta property="og:type" content="website">' . "\n";
    echo '<meta property="og:site_name" content="Mojoy">' . "\n";
    
    // Twitter Cards
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    echo '<meta name="twitter:site" content="@mojoy">' . "\n";
    echo '<meta name="twitter:title" content="' . esc_attr($og_title) . '">' . "\n";
    echo '<meta name="twitter:description" content="' . esc_attr($og_description) . '">' . "\n";
    echo '<meta name="twitter:image" content="' . esc_url($og_image) . '">' . "\n";
}

/**
 * Custom Title Parts
 */
function mojoy_custom_title_parts($title_parts) {
    if (is_front_page()) {
        $title_parts['title'] = 'Mojoy - Feel Good, Do Good | Premium Comfort Apparel with Purpose';
    } elseif (is_shop()) {
        $title_parts['title'] = 'Shop Premium Comfort Collection | Mojoy';
    } elseif (is_product_category()) {
        $title_parts['title'] = single_term_title('', false) . ' | Premium Comfort | Mojoy';
    }
    
    return $title_parts;
}

/**
 * Performance Optimizations
 */
function mojoy_performance_optimizations() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emojis
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');
    
    // Optimize scripts and styles
    add_action('wp_enqueue_scripts', 'mojoy_optimize_scripts', 100);
    
    // Add critical CSS
    add_action('wp_head', 'mojoy_critical_css', 1);
    
    // Lazy load images
    add_filter('wp_get_attachment_image_attributes', 'mojoy_lazy_load_images', 10, 3);
}
add_action('init', 'mojoy_performance_optimizations');

/**
 * Optimize Scripts and Styles
 */
function mojoy_optimize_scripts() {
    // Defer non-critical JavaScript
    if (!is_admin()) {
        wp_script_add_data('jquery', 'defer', true);
        wp_script_add_data('kadence-navigation', 'defer', true);
    }
    
    // Remove unnecessary scripts
    wp_dequeue_script('wp-embed');
    
    // Optimize WooCommerce scripts
    if (class_exists('WooCommerce')) {
        if (!is_woocommerce() && !is_cart() && !is_checkout()) {
            wp_dequeue_script('wc-cart-fragments');
            wp_dequeue_script('woocommerce');
            wp_dequeue_style('woocommerce-general');
            wp_dequeue_style('woocommerce-layout');
            wp_dequeue_style('woocommerce-smallscreen');
        }
    }
}

/**
 * Critical CSS for Above-the-Fold Content
 */
function mojoy_critical_css() {
    ?>
    <style>
    /* Critical CSS for above-the-fold content */
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        margin: 0;
        padding: 0;
        background: #ffffff;
        color: #333333;
        line-height: 1.6;
    }
    
    .mojoy-hero-section {
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem;
    }
    
    .mojoy-hero-title {
        font-size: clamp(2.5rem, 5vw, 4rem);
        font-weight: 700;
        color: #1a252f;
        margin-bottom: 1rem;
        letter-spacing: -0.02em;
    }
    
    .mojoy-btn-primary {
        background: #e74c3c;
        color: white;
        padding: 16px 32px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        display: inline-block;
        transition: transform 0.3s ease;
    }
    
    .mojoy-btn-primary:hover {
        transform: translateY(-2px);
    }
    
    /* Header styles */
    .site-header {
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        position: sticky;
        top: 0;
        z-index: 1000;
    }
    
    /* Loading optimization */
    img {
        max-width: 100%;
        height: auto;
    }
    
    .lazy {
        opacity: 0;
        transition: opacity 0.3s;
    }
    
    .lazy.loaded {
        opacity: 1;
    }
    </style>
    <?php
}

/**
 * Lazy Load Images
 */
function mojoy_lazy_load_images($attr, $attachment, $size) {
    if (!is_admin()) {
        $attr['loading'] = 'lazy';
        $attr['decoding'] = 'async';
    }
    return $attr;
}

/**
 * Add Sitemap Generation
 */
function mojoy_generate_sitemap() {
    if (isset($_GET['sitemap']) && $_GET['sitemap'] === 'xml') {
        header('Content-Type: application/xml; charset=utf-8');
        
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // Homepage
        echo '<url>' . "\n";
        echo '<loc>' . home_url() . '</loc>' . "\n";
        echo '<changefreq>daily</changefreq>' . "\n";
        echo '<priority>1.0</priority>' . "\n";
        echo '</url>' . "\n";
        
        // Pages
        $pages = get_pages();
        foreach ($pages as $page) {
            echo '<url>' . "\n";
            echo '<loc>' . get_permalink($page->ID) . '</loc>' . "\n";
            echo '<lastmod>' . date('Y-m-d', strtotime($page->post_modified)) . '</lastmod>' . "\n";
            echo '<changefreq>weekly</changefreq>' . "\n";
            echo '<priority>0.8</priority>' . "\n";
            echo '</url>' . "\n";
        }
        
        // Products
        if (class_exists('WooCommerce')) {
            $products = get_posts(array(
                'post_type' => 'product',
                'posts_per_page' => -1,
                'post_status' => 'publish'
            ));
            
            foreach ($products as $product) {
                echo '<url>' . "\n";
                echo '<loc>' . get_permalink($product->ID) . '</loc>' . "\n";
                echo '<lastmod>' . date('Y-m-d', strtotime($product->post_modified)) . '</lastmod>' . "\n";
                echo '<changefreq>weekly</changefreq>' . "\n";
                echo '<priority>0.7</priority>' . "\n";
                echo '</url>' . "\n";
            }
        }
        
        echo '</urlset>' . "\n";
        exit;
    }
}
add_action('init', 'mojoy_generate_sitemap');

/**
 * Add robots.txt optimization
 */
function mojoy_robots_txt($output) {
    $output .= "User-agent: *\n";
    $output .= "Disallow: /wp-admin/\n";
    $output .= "Disallow: /wp-includes/\n";
    $output .= "Disallow: /wp-content/plugins/\n";
    $output .= "Disallow: /wp-content/themes/\n";
    $output .= "Allow: /wp-content/uploads/\n";
    $output .= "Sitemap: " . home_url() . "/?sitemap=xml\n";
    
    return $output;
}
add_filter('robots_txt', 'mojoy_robots_txt');

/**
 * Security Headers
 */
function mojoy_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}
add_action('send_headers', 'mojoy_security_headers');
