<?php
/**
 * MOJOY AUTOMATED SETUP SCRIPT
 * Run this script to automatically set up the complete Mojoy website
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('administrator')) {
    die('You must be an administrator to run this script.');
}

echo "🌟 MOJOY SETUP STARTING...\n";

// 1. Update site settings
update_option('blogname', 'Mojoy');
update_option('blogdescription', 'Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact');
echo "✅ Site title and tagline updated\n";

// 2. Create pages
$pages = array(
    'Home' => array(
        'content' => file_get_contents('wp-content/mojoy-homepage-complete.html'),
        'template' => 'page'
    ),
    'About' => array(
        'content' => file_get_contents('wp-content/mojoy-about-page.html'),
        'template' => 'page'
    ),
    'Contact' => array(
        'content' => file_get_contents('wp-content/mojoy-essential-pages.html'),
        'template' => 'page'
    ),
    'FAQ' => array(
        'content' => file_get_contents('wp-content/mojoy-essential-pages.html'),
        'template' => 'page'
    ),
    'Shipping & Returns' => array(
        'content' => file_get_contents('wp-content/mojoy-shipping-returns.html'),
        'template' => 'page'
    )
);

$created_pages = array();

foreach ($pages as $title => $page_data) {
    $existing_page = get_page_by_title($title);
    
    if (!$existing_page) {
        $page_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $page_data['content'],
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_author' => 1
        ));
        
        if ($page_id) {
            $created_pages[$title] = $page_id;
            echo "✅ Created page: $title (ID: $page_id)\n";
        }
    } else {
        $created_pages[$title] = $existing_page->ID;
        echo "ℹ️ Page already exists: $title\n";
    }
}

// 3. Set homepage
if (isset($created_pages['Home'])) {
    update_option('show_on_front', 'page');
    update_option('page_on_front', $created_pages['Home']);
    echo "✅ Homepage set\n";
}

// 4. Create navigation menu
$menu_name = 'Primary Menu';
$menu_exists = wp_get_nav_menu_object($menu_name);

if (!$menu_exists) {
    $menu_id = wp_create_nav_menu($menu_name);
    
    // Add menu items
    $menu_items = array(
        array('title' => 'Home', 'url' => home_url()),
        array('title' => 'Shop', 'url' => home_url('/shop')),
        array('title' => 'About', 'url' => get_permalink($created_pages['About'])),
        array('title' => 'Contact', 'url' => get_permalink($created_pages['Contact']))
    );
    
    foreach ($menu_items as $item) {
        wp_update_nav_menu_item($menu_id, 0, array(
            'menu-item-title' => $item['title'],
            'menu-item-url' => $item['url'],
            'menu-item-status' => 'publish'
        ));
    }
    
    // Set menu location
    $locations = get_theme_mod('nav_menu_locations');
    $locations['primary'] = $menu_id;
    set_theme_mod('nav_menu_locations', $locations);
    
    echo "✅ Navigation menu created and assigned\n";
}

// 5. Configure WooCommerce basic settings
if (class_exists('WooCommerce')) {
    // Set currency to INR
    update_option('woocommerce_currency', 'INR');
    
    // Set country to India
    update_option('woocommerce_default_country', 'IN');
    
    // Enable guest checkout
    update_option('woocommerce_enable_guest_checkout', 'yes');
    
    // Set shop page
    $shop_page = get_page_by_title('Shop');
    if (!$shop_page) {
        $shop_page_id = wp_insert_post(array(
            'post_title' => 'Shop',
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page'
        ));
        update_option('woocommerce_shop_page_id', $shop_page_id);
    }
    
    // Set cart page
    $cart_page = get_page_by_title('Cart');
    if (!$cart_page) {
        $cart_page_id = wp_insert_post(array(
            'post_title' => 'Cart',
            'post_content' => '[woocommerce_cart]',
            'post_status' => 'publish',
            'post_type' => 'page'
        ));
        update_option('woocommerce_cart_page_id', $cart_page_id);
    }
    
    // Set checkout page
    $checkout_page = get_page_by_title('Checkout');
    if (!$checkout_page) {
        $checkout_page_id = wp_insert_post(array(
            'post_title' => 'Checkout',
            'post_content' => '[woocommerce_checkout]',
            'post_status' => 'publish',
            'post_type' => 'page'
        ));
        update_option('woocommerce_checkout_page_id', $checkout_page_id);
    }
    
    // Set my account page
    $account_page = get_page_by_title('My Account');
    if (!$account_page) {
        $account_page_id = wp_insert_post(array(
            'post_title' => 'My Account',
            'post_content' => '[woocommerce_my_account]',
            'post_status' => 'publish',
            'post_type' => 'page'
        ));
        update_option('woocommerce_myaccount_page_id', $account_page_id);
    }
    
    echo "✅ WooCommerce pages configured\n";
}

// 6. Create sample products (trigger the function from our integration)
if (function_exists('mojoy_create_sample_products')) {
    mojoy_create_sample_products();
    echo "✅ Sample products created\n";
} else {
    echo "⚠️ Sample products function not available\n";
}

// 7. Set permalink structure
global $wp_rewrite;
$wp_rewrite->set_permalink_structure('/%postname%/');
$wp_rewrite->flush_rules();
echo "✅ Permalink structure updated\n";

// 8. Update theme customizer settings
set_theme_mod('custom_logo', '');
set_theme_mod('header_textcolor', '#1a252f');
set_theme_mod('background_color', '#ffffff');

echo "✅ Theme customizer settings updated\n";

// 9. Create product categories
if (class_exists('WooCommerce')) {
    $categories = array(
        'T-Shirts' => 'Premium comfort t-shirts crafted from the finest materials',
        'Polos' => 'Elevated polo shirts for work and weekend',
        'Essentials' => 'Wardrobe essentials for everyday comfort',
        'New Arrivals' => 'Latest additions to our comfort collection',
        'Bestsellers' => 'Customer favorites and top-rated items'
    );
    
    foreach ($categories as $name => $description) {
        if (!term_exists($name, 'product_cat')) {
            wp_insert_term($name, 'product_cat', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
            echo "✅ Created product category: $name\n";
        }
    }
}

// 10. Final optimizations
// Clear any caches
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
}

// Update rewrite rules
flush_rewrite_rules();

echo "\n🎉 MOJOY SETUP COMPLETED SUCCESSFULLY!\n";
echo "🌟 Your website is now ready with:\n";
echo "   - Homepage with brand story and impact messaging\n";
echo "   - About page with Kitchen Initiative details\n";
echo "   - Complete WooCommerce shop setup\n";
echo "   - Sample products with variations\n";
echo "   - Navigation menu\n";
echo "   - SEO optimization\n";
echo "   - Mobile-responsive design\n";
echo "\n💡 Next steps:\n";
echo "   1. Add your product images\n";
echo "   2. Configure payment methods\n";
echo "   3. Set up shipping zones\n";
echo "   4. Test the checkout process\n";
echo "\n🚀 Visit your website to see the results!\n";

?>
