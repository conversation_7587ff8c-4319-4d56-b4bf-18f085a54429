#!/bin/bash

echo "🌟 MOJOY WEBSITE SETUP STARTING..."
echo "Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact"
echo "=================================================="

# Set WP-CLI to allow root
WP_CLI="wp --allow-root"

# 1. Update site settings
echo "✅ Updating site settings..."
$WP_CLI option update blogname "Mojoy"
$WP_CLI option update blogdescription "Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact"

# 2. Create homepage
echo "✅ Creating homepage..."
HOME_CONTENT='<!-- wp:kadence/rowlayout {"uniqueID":"_hero001","columns":1,"colLayout":"equal","background":{"color":"#ffffff"},"padding":["120px","20px","120px","20px"]} -->
<div class="wp-block-kadence-rowlayout alignnone kt-row-layout-inner kt-layout-id_hero001 mojoy-hero-section">
    <div class="kt-row-column-wrap kt-has-1-columns kt-gutter-default kt-v-gutter-default kt-row-valign-middle kt-row-layout-equal">
        <div class="wp-block-kadence-column kadence-column_hero002">
            <div class="kt-inside-inner-col">
                <!-- wp:heading {"textAlign":"center","level":1,"style":{"typography":{"fontSize":"4rem","fontWeight":"700"},"color":{"text":"#1a252f"}}} -->
                <h1 class="wp-block-heading has-text-align-center">MOJOY</h1>
                <!-- /wp:heading -->
                
                <!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"1.5rem","fontWeight":"400"},"color":{"text":"#2c3e50"}}} -->
                <p class="has-text-align-center"><strong>Feel Good, Do Good</strong></p>
                <!-- /wp:paragraph -->
                
                <!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"1.1rem","fontWeight":"600","textTransform":"uppercase"},"color":{"text":"#e74c3c"}}} -->
                <p class="has-text-align-center">Premium Comfort • Timeless Style • Purposeful Impact</p>
                <!-- /wp:paragraph -->
                
                <!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"1.2rem"},"color":{"text":"#333333"}}} -->
                <p class="has-text-align-center">At Mojoy, we believe premium comfort should move with you—and move the world forward. Every purchase funds meals for underserved communities through our Kitchen Initiative.</p>
                <!-- /wp:paragraph -->
                
                <!-- wp:buttons {"layout":{"type":"flex","justifyContent":"center"}} -->
                <div class="wp-block-buttons">
                    <!-- wp:button {"style":{"color":{"background":"#e74c3c","text":"#ffffff"},"border":{"radius":"8px"}}} -->
                    <div class="wp-block-button"><a class="wp-block-button__link wp-element-button" href="/shop">Shop Now</a></div>
                    <!-- /wp:button -->
                    
                    <!-- wp:button {"style":{"color":{"text":"#1a252f","background":"transparent"},"border":{"color":"#1a252f","width":"2px","radius":"8px"}}} -->
                    <div class="wp-block-button"><a class="wp-block-button__link wp-element-button" href="/about">Our Story</a></div>
                    <!-- /wp:button -->
                </div>
                <!-- /wp:buttons -->
            </div>
        </div>
    </div>
</div>
<!-- /wp:kadence/rowlayout -->'

HOME_ID=$($WP_CLI post create --post_type=page --post_title="Home" --post_content="$HOME_CONTENT" --post_status=publish --porcelain)
echo "Created homepage with ID: $HOME_ID"

# 3. Create About page
echo "✅ Creating About page..."
ABOUT_CONTENT='<!-- wp:heading {"textAlign":"center","level":1} -->
<h1 class="wp-block-heading has-text-align-center">Our Story</h1>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center"} -->
<p class="has-text-align-center">What if your favorite t-shirt didn't just feel good—but did good too?</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":2} -->
<h2 class="wp-block-heading">The Meaning Behind Mojoy</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>The name Mojoy is inspired by "Moments of Joy"—the little things that make life special. Whether it's the softness of a perfectly worn-in tee, the breathability of a summer polo, or the ease of slipping into your go-to essentials, Mojoy is designed to make you feel good, inside and out.</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":2} -->
<h2 class="wp-block-heading">The Mojoy Kitchen Initiative</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>True comfort goes beyond clothing. With every Mojoy purchase, you're helping put food on someone's plate. Through the Mojoy Kitchen Initiative, we allocate a portion of our profits to provide nutritious meals for underserved communities.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p><strong>💛 Your moment of comfort contributes to someone else's moment of nourishment.</strong></p>
<!-- /wp:paragraph -->'

ABOUT_ID=$($WP_CLI post create --post_type=page --post_title="About" --post_content="$ABOUT_CONTENT" --post_status=publish --porcelain)
echo "Created About page with ID: $ABOUT_ID"

# 4. Set homepage
echo "✅ Setting homepage..."
$WP_CLI option update show_on_front page
$WP_CLI option update page_on_front $HOME_ID

# 5. Create navigation menu
echo "✅ Creating navigation menu..."
MENU_ID=$($WP_CLI menu create "Primary Menu" --porcelain)
$WP_CLI menu item add-post $MENU_ID $HOME_ID --title="Home"
$WP_CLI menu item add-custom $MENU_ID "Shop" "/shop"
$WP_CLI menu item add-post $MENU_ID $ABOUT_ID --title="About"
$WP_CLI menu item add-custom $MENU_ID "Contact" "/contact"

# Assign menu to primary location
$WP_CLI menu location assign $MENU_ID primary

# 6. Configure WooCommerce
echo "✅ Configuring WooCommerce..."
$WP_CLI option update woocommerce_currency INR
$WP_CLI option update woocommerce_default_country IN
$WP_CLI option update woocommerce_enable_guest_checkout yes

# Create WooCommerce pages
SHOP_ID=$($WP_CLI post create --post_type=page --post_title="Shop" --post_status=publish --porcelain)
CART_ID=$($WP_CLI post create --post_type=page --post_title="Cart" --post_content="[woocommerce_cart]" --post_status=publish --porcelain)
CHECKOUT_ID=$($WP_CLI post create --post_type=page --post_title="Checkout" --post_content="[woocommerce_checkout]" --post_status=publish --porcelain)
ACCOUNT_ID=$($WP_CLI post create --post_type=page --post_title="My Account" --post_content="[woocommerce_my_account]" --post_status=publish --porcelain)

$WP_CLI option update woocommerce_shop_page_id $SHOP_ID
$WP_CLI option update woocommerce_cart_page_id $CART_ID
$WP_CLI option update woocommerce_checkout_page_id $CHECKOUT_ID
$WP_CLI option update woocommerce_myaccount_page_id $ACCOUNT_ID

# 7. Create product categories
echo "✅ Creating product categories..."
$WP_CLI term create product_cat "T-Shirts" --description="Premium comfort t-shirts crafted from the finest materials"
$WP_CLI term create product_cat "Polos" --description="Elevated polo shirts for work and weekend"
$WP_CLI term create product_cat "Essentials" --description="Wardrobe essentials for everyday comfort"

# 8. Create sample products
echo "✅ Creating sample products..."

# Essential Comfort Tee
TEE_ID=$($WP_CLI wc product create --name="Essential Comfort Tee" --type=simple --regular_price=899 --sale_price=699 --description="Our signature ultra-soft t-shirt crafted from premium organic cotton. Every purchase funds 2 meals through our Kitchen Initiative." --short_description="Ultra-soft organic cotton tee with timeless comfort and purposeful impact." --sku="MOJOY-TEE-001" --featured=true --status=publish --porcelain)

# Breathe Easy Polo
POLO_ID=$($WP_CLI wc product create --name="Breathe Easy Polo" --type=simple --regular_price=1299 --description="Elevate your style with our premium polo shirt featuring moisture-wicking technology and breathable fabric. Funds 2 meals per purchase." --short_description="Premium polo with moisture-wicking technology and breathable comfort." --sku="MOJOY-POLO-001" --featured=true --status=publish --porcelain)

# Movement Henley
HENLEY_ID=$($WP_CLI wc product create --name="Movement Henley" --type=simple --regular_price=1099 --description="A versatile henley that transitions seamlessly from day to night. Made with our signature soft-touch fabric that feels like a second skin." --short_description="Versatile henley with signature soft-touch fabric for all-day comfort." --sku="MOJOY-HENLEY-001" --status=publish --porcelain)

echo "Created products: Tee ($TEE_ID), Polo ($POLO_ID), Henley ($HENLEY_ID)"

# 9. Set permalink structure
echo "✅ Setting permalink structure..."
$WP_CLI rewrite structure '/%postname%/'
$WP_CLI rewrite flush

# 10. Final optimizations
echo "✅ Final optimizations..."
$WP_CLI cache flush

echo ""
echo "🎉 MOJOY SETUP COMPLETED SUCCESSFULLY!"
echo "=================================================="
echo "🌟 Your website is now ready with:"
echo "   ✅ Homepage with brand story and impact messaging"
echo "   ✅ About page with Kitchen Initiative details"
echo "   ✅ Complete WooCommerce shop setup"
echo "   ✅ Sample products with impact messaging"
echo "   ✅ Navigation menu"
echo "   ✅ SEO-friendly permalinks"
echo ""
echo "💡 Next steps:"
echo "   1. Add product images"
echo "   2. Configure payment methods"
echo "   3. Set up shipping zones"
echo "   4. Test the checkout process"
echo ""
echo "🚀 Visit your website to see the results!"
echo "Feel Good, Do Good - Premium Comfort, Timeless Style, Purposeful Impact"
